/**
 * WebView基础功能混入
 * 只负责webview的基本加载、事件绑定等，不涉及业务逻辑
 */
export default {
  data() {
    return {
      // webview基础状态
      webviewUrl: '', // webview当前加载的URL
      webviewLoading: false,
      webviewLoaded: false,
      webviewEventHandlers: null
    }
  },

  beforeDestroy() {
    this.cleanupWebview()
  },

  methods: {
    /**
     * 加载webview - 纯粹的加载功能
     */
    loadWebview(url) {
      if (!url) {
        this.$message.warning('请输入网页地址')
        return
      }

      // 如果URL相同且已加载，直接刷新
      if (this.webviewUrl === url && this.webviewLoaded) {
        this.refreshWebview()
        return
      }

      this.webviewUrl = url
      this.webviewLoading = true
      this.webviewLoaded = false

      this.$message.info('正在加载网页...')

      // 等待webview创建后绑定事件
      this.$nextTick(() => {
        setTimeout(() => {
          this.bindWebviewEvents()
        }, 100)
      })
    },

    /**
     * 绑定webview基础事件
     */
    bindWebviewEvents() {
      const webview = this.$refs.webview
      if (!webview) {
        console.log('webview引用不存在')
        return
      }

      console.log('绑定webview基础事件')

      // 清除之前的事件监听器
      if (this.webviewEventHandlers) {
        this.removeWebviewEvents()
      }

      // 创建事件处理器
      this.webviewEventHandlers = {
        domReady: () => {
          console.log('webview DOM ready')
          this.webviewLoading = false
          this.webviewLoaded = true
          this.$message.success('网页加载完成')

          // 注入链接拦截脚本
          // if (this.injectPureLinkInterceptor) {
          //   this.injectPureLinkInterceptor()
          // }

          // 触发自定义事件
          this.$emit('webview-ready')
        },

        startLoading: () => {
          console.log('webview start loading')
          // this.webviewLoading = true
        },

        stopLoading: () => {
          console.log('webview stop loading')
          this.webviewLoading = false
        },

        failLoad: (event) => {
          console.error('webview load failed:', event)
          this.webviewLoading = false
          // 只在严重错误时提示
          if (event.errorCode === -106) {
            // 网络断开
            this.$message.error('网络连接已断开')
          }
        },

        titleUpdated: (event) => {
          console.log('页面标题:', event.title)
          this.$emit('title-updated', event.title)
        },

        // 新窗口请求处理
        newWindow: (event) => {
          if (this.handleNewWindow) {
            this.handleNewWindow(event)
          }
        }
      }

      // 绑定事件
      webview.addEventListener('dom-ready', this.webviewEventHandlers.domReady)
      webview.addEventListener('did-start-loading', this.webviewEventHandlers.startLoading)
      webview.addEventListener('did-stop-loading', this.webviewEventHandlers.stopLoading)
      webview.addEventListener('did-fail-load', this.webviewEventHandlers.failLoad)
      webview.addEventListener('page-title-updated', this.webviewEventHandlers.titleUpdated)
      // webview.addEventListener('new-window', this.webviewEventHandlers.newWindow)
      webview.openDevTools()
    },

    /**
     * 移除webview事件监听器
     */
    removeWebviewEvents() {
      const webview = this.$refs.webview
      if (!webview || !this.webviewEventHandlers) return

      console.log('移除webview事件监听器')

      webview.removeEventListener('dom-ready', this.webviewEventHandlers.domReady)
      webview.removeEventListener('did-start-loading', this.webviewEventHandlers.startLoading)
      webview.removeEventListener('did-stop-loading', this.webviewEventHandlers.stopLoading)
      webview.removeEventListener('did-fail-load', this.webviewEventHandlers.failLoad)
      webview.removeEventListener('page-title-updated', this.webviewEventHandlers.titleUpdated)
      webview.removeEventListener('new-window', this.webviewEventHandlers.newWindow)

      this.webviewEventHandlers = null
    },

    /**
     * 刷新webview
     */
    refreshWebview() {
      if (!this.$refs.webview) {
        this.$message.warning('webview未准备就绪')
        return
      }

      this.webviewLoading = true
      this.webviewLoaded = false

      try {
        this.$refs.webview.reload()
        this.$message.info('正在刷新页面...')
      } catch (error) {
        console.error('刷新webview失败:', error)
        this.webviewLoading = false
        this.$message.error('刷新失败，请重新加载')
      }
    },

    /**
     * 清理webview资源
     */
    cleanupWebview() {
      this.removeWebviewEvents()
      console.log('webview资源已清理')
    }
  }
}
