<template>
  <div class="app-container">
    <!-- 输入和控制区域 -->
    <el-row :gutter="10" style="margin-bottom: 20px">
      <el-col :span="16">
        <el-input v-model="url" placeholder="请输入网页地址" clearable>
          <template slot="prepend">🌐</template>
        </el-input>
        <!-- 显示当前页面URL -->
        <div v-if="displayUrl && displayUrl !== url" class="current-url-info">
          <i class="el-icon-info"></i> 当前页面: <span class="url-text">{{ displayUrl }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <el-button type="primary" :disabled="!url" @click="handleLoadWebview">
          {{ webviewLoading ? '加载中...' : '🌐 加载预览' }}
        </el-button>
        <el-button type="success" :disabled="!webviewLoaded" @click="extractContentFromWebview">
          {{ extracting ? '提取中...' : '📄 提取内容' }}
        </el-button>
        <el-button type="info" :disabled="!webviewLoaded" @click="checkLoginStatus"> 🔍 检查登录状态 </el-button>
        <el-button type="warning" :disabled="!webviewLoaded" @click="refreshWebview"> 🔄 刷新 </el-button>
      </el-col>
    </el-row>

    <!-- 功能说明 -->
    <el-row :gutter="10" style="margin-bottom: 20px">
      <el-col :span="24">
        <el-alert title="智能内容提取" type="info" show-icon :closable="false">
          <template slot="title">
            <span>🎯 智能内容提取 + 登录状态保持 + 链接拦截</span>
          </template>
          <div>
            <p><strong>🚀 一体化操作流程：</strong></p>
            <ul style="margin: 5px 0; padding-left: 20px; color: #67c23a">
              <li>✅ 1️⃣ 输入网址，点击"🌐 加载预览"</li>
              <li>✅ 2️⃣ 在下方预览区域中直接浏览，所有链接自动在当前窗口打开</li>
              <li>✅ 3️⃣ 登录完成后，点击"📄 提取内容"获取数据</li>
              <li>✅ 4️⃣ 可使用"🔍 检查登录状态"确认登录状态</li>
            </ul>
            <p style="color: #e6a23c; margin: 10px 0">💡 <strong>关键优势</strong>：预览区域与主程序共享登录状态，链接自动拦截，无需多窗口操作！</p>
          </div>
        </el-alert>
      </el-col>
    </el-row>

    <!-- 网页预览区域 -->
    <div style="margin-bottom: 20px">
      <h3>
        网页预览
        <span v-if="webviewLoaded" style="margin-left: 10px; color: #67c23a; font-size: 14px">
          ✅ 已加载 |
          <span v-if="loginStatus.isLoggedIn" style="color: #67c23a">🔐 已登录</span>
          <span v-else style="color: #e6a23c">🔓 未登录</span>
        </span>
      </h3>

      <!-- webview预览区域 -->
      <div class="webview-container" style="height: 600px; border: 1px solid #ddd; border-radius: 4px; overflow: hidden">
        <webview v-if="webviewUrl" ref="webview" :src="webviewUrl" style="width: 100%; height: 100%" partition="persist:main"></webview>

        <!-- 空状态提示 -->
        <div v-else class="empty-webview" style="height: 100%; display: flex; align-items: center; justify-content: center; background: #f5f5f5">
          <div style="text-align: center; color: #999">
            <i class="el-icon-monitor" style="font-size: 48px; margin-bottom: 16px"></i>
            <p>请输入网址并点击"🌐 加载预览"开始浏览</p>
            <p style="font-size: 12px">支持登录状态保持，链接自动拦截，可直接在此区域完成网站登录和浏览</p>
          </div>
        </div>

        <!-- 加载状态遮罩 -->
        <div
          v-if="webviewLoading"
          class="loading-overlay"
          style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255, 255, 255, 0.8); display: flex; align-items: center; justify-content: center; z-index: 10"
        >
          <div style="text-align: center">
            <i class="el-icon-loading" style="font-size: 24px; color: #409eff"></i>
            <p style="margin-top: 8px; color: #409eff">页面加载中...</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容分析结果显示 -->
    <div v-if="webContent" style="margin-bottom: 20px">
      <h3>内容分析结果</h3>
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="网页标题">{{ webContent.title || '无' }}</el-descriptions-item>
            <el-descriptions-item label="网页URL">{{ webContent.url || '无' }}</el-descriptions-item>
            <el-descriptions-item label="提取规则">{{ webContent.extractionRule || '默认规则' }}</el-descriptions-item>
            <el-descriptions-item label="匹配选择器">{{ webContent.extractedSelector || '无匹配' }}</el-descriptions-item>
            <el-descriptions-item label="获取时间">{{ webContent.fetchTime || '无' }}</el-descriptions-item>
            <el-descriptions-item label="内容长度">{{ webContent.textContent ? webContent.textContent.length + ' 字符' : '0 字符' }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>

        <el-tab-pane label="提取内容" name="specific">
          <div v-if="webContent.specificContent">
            <el-button size="small" @click="copyToClipboard(webContent.specificContent)">复制内容</el-button>
            <div class="content-display" v-html="webContent.specificContent"></div>
          </div>
          <el-empty v-else description="未找到匹配的内容元素"></el-empty>
        </el-tab-pane>

        <el-tab-pane label="纯文本内容" name="textContent">
          <div v-if="webContent.textContent">
            <el-button size="small" @click="copyToClipboard(webContent.textContent)">复制文本</el-button>
            <div class="text-content">{{ webContent.textContent }}</div>
          </div>
          <el-empty v-else description="无文本内容"></el-empty>
        </el-tab-pane>

        <el-tab-pane label="markdown" name="markdown">
          <div v-if="webContent.markDownText">
            <el-button size="small" @click="copyToClipboard(webContent.markDownText)">复制文本</el-button>
            <div class="text-content">{{ webContent.markDownText }}</div>
          </div>
          <el-empty v-else description="无文本内容"></el-empty>
        </el-tab-pane>

        <el-tab-pane label="完整HTML" name="html">
          <el-button size="small" @click="copyToClipboard(webContent.html)">复制HTML</el-button>
          <pre class="html-content">{{ webContent.html }}</pre>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import TurndownService from 'turndown'
import { callBailianAPI } from '@/utils/aiRequest'
import websiteUtils from './websiteUtils.js'

export default {
  name: 'BiddingAnalysis',

  data() {
    return {
      url: '', // 输入框绑定的URL
      displayUrl: '', // 显示用的当前URL

      // webview状态
      webviewUrl: '',
      webviewLoading: false,
      webviewLoaded: false,

      // 内容提取
      webContent: null,
      activeTab: 'basic',
      extracting: false,

      // 登录状态
      loginStatus: {
        isLoggedIn: false,
        hasLoginCookie: false,
        hasLoginElement: false,
        needLogin: false
      }
    }
  },

  mounted() {
    console.log('招投标分析组件已挂载')
  },

  beforeDestroy() {
    this.cleanupWebview()
  },

  methods: {
    /**
     * 处理加载webview按钮点击 - 最简单的实现
     */
    handleLoadWebview() {
      if (!this.url) {
        this.$message.warning('请输入网页地址')
        return
      }

      console.log('开始加载:', this.url)
      this.webviewUrl = this.url
      this.displayUrl = this.url
      this.webviewLoading = true
      this.webviewLoaded = false

      // 等待webview创建后绑定最基本的事件
      this.$nextTick(() => {
        setTimeout(() => {
          this.bindBasicEvents()
        }, 100)
      })
    },

    /**
     * 绑定最基本的webview事件 - 只处理初始加载
     */
    bindBasicEvents() {
      const webview = this.$refs.webview
      if (!webview) return

      console.log('绑定基本事件')

      // 只监听初始DOM准备事件，后续跳转不再监听任何事件
      const domReadyHandler = () => {
        console.log('DOM ready - 初始页面加载完成')
        this.webviewLoading = false
        this.webviewLoaded = true
        this.$message.success('网页加载完成')

        // 注入链接修复脚本，让所有跳转都用window.location.href
        this.injectSimpleLinkFix()

        // 重要：移除这个事件监听器，避免后续跳转时重复触发
        // webview.removeEventListener('dom-ready', domReadyHandler)
      }

      webview.addEventListener('dom-ready', domReadyHandler)

      // 只处理真正的加载失败，不处理跳转
      webview.addEventListener('did-fail-load', (event) => {
        // 只处理初始加载失败，跳转失败不处理
        if (this.webviewLoading) {
          console.error('初始加载失败:', event)
          this.webviewLoading = false
          if (event.errorCode === -106) {
            this.$message.error('网络连接已断开')
          }
        }
      })
    },

    /**
     * 注入最简单的链接修复 - 让所有跳转都用window.location.href
     */
    async injectSimpleLinkFix() {
      if (!this.$refs.webview) return

      try {
        await this.$refs.webview.executeJavaScript(`
          (function() {
            if (window.linkFixInjected) return;
            window.linkFixInjected = true;

            console.log('注入链接修复脚本');

            // 修改所有新窗口链接为当前窗口
            document.addEventListener('click', function(event) {
              const link = event.target.closest('a');
              if (link && (link.target === '_blank' || link.target === '_new')) {
                console.log('修复链接:', link.href);
                link.target = '_self';
                // 不阻止默认行为，让浏览器自然跳转
              }
            });

            // 重写window.open为当前窗口跳转
            window.open = function(url) {
              console.log('拦截window.open，改为当前窗口跳转:', url);
              if (url) {
                window.location.href = url; // 直接跳转，不需要任何额外处理
              }
              return null;
            };

            // 处理表单新窗口提交
            document.addEventListener('submit', function(event) {
              const form = event.target;
              if (form.target === '_blank') {
                console.log('修复表单target:', form.action);
                form.target = '_self';
              }
            });

            console.log('链接修复脚本注入完成');
          })();
        `)

        // 启动URL监控（定期获取当前URL并更新显示）
        this.startUrlMonitoring()
      } catch (error) {
        console.error('注入链接修复脚本失败:', error)
      }
    },

    /**
     * 启动URL监控 - 定期获取当前URL并更新显示
     */
    startUrlMonitoring() {
      // 每2秒检查一次URL变化
      this.urlMonitorTimer = setInterval(async () => {
        if (!this.$refs.webview) return

        try {
          const currentUrl = await this.$refs.webview.executeJavaScript('window.location.href')
          if (currentUrl && currentUrl !== this.displayUrl && currentUrl.startsWith('http')) {
            console.log('检测到URL变化:', currentUrl)
            this.displayUrl = currentUrl
          }
        } catch (error) {
          // 静默处理错误，不影响用户体验
        }
      }, 2000)
    },

    /**
     * 刷新webview
     */
    refreshWebview() {
      if (!this.$refs.webview) return

      this.webviewLoading = true
      this.$refs.webview.reload()
    },

    /**
     * 清理资源
     */
    cleanupWebview() {
      if (this.urlMonitorTimer) {
        clearInterval(this.urlMonitorTimer)
        this.urlMonitorTimer = null
      }
      console.log('清理webview资源')
    },

    /**
     * 检查登录状态 - 简单实现
     */
    async checkLoginStatus() {
      if (!this.$refs.webview || !this.webviewLoaded) {
        this.$message.warning('请先加载网页')
        return
      }

      try {
        const result = await this.$refs.webview.executeJavaScript(`
          (function() {
            const cookies = document.cookie;
            const url = window.location.href;
            const title = document.title;

            // 简单检查登录状态
            const hasLoginCookie = cookies.includes('login') || cookies.includes('session') || cookies.includes('token');
            const hasLogoutElement = document.querySelector('.logout, [href*="logout"]');
            const hasLoginForm = document.querySelector('form[action*="login"], .login-form');

            return {
              url: url,
              title: title,
              hasLoginCookie: hasLoginCookie,
              hasLogoutElement: !!hasLogoutElement,
              hasLoginForm: !!hasLoginForm,
              isLoggedIn: hasLoginCookie || !!hasLogoutElement
            };
          })()
        `)

        this.loginStatus = result

        if (result.isLoggedIn) {
          this.$message.success('✅ 检测到已登录状态')
        } else {
          this.$message.info('ℹ️ 未检测到登录状态')
        }
      } catch (error) {
        console.error('检查登录状态失败:', error)
        this.$message.error('检查登录状态失败')
      }
    },

    /**
     * 提取网页内容 - 简单实现
     */
    async extractContentFromWebview() {
      if (!this.$refs.webview || !this.webviewLoaded) {
        this.$message.warning('请先加载网页')
        return
      }

      this.extracting = true

      try {
        const result = await this.$refs.webview.executeJavaScript(`
          (function() {
            return {
              title: document.title,
              url: window.location.href,
              html: document.body.innerHTML,
              textContent: document.body.textContent.replace(/\\s+/g, ' ').trim(),
              extractionTime: new Date().toLocaleString()
            };
          })()
        `)

        // 简单的markdown转换
        const turndownService = new TurndownService()
        const markdown = turndownService.turndown(result.html)

        this.webContent = {
          ...result,
          markDownText: markdown,
          extractionRule: '通用提取',
          fetchTime: new Date().toLocaleString()
        }

        this.activeTab = 'basic'
        this.extracting = false
        this.$message.success('✅ 内容提取成功！')
      } catch (error) {
        this.extracting = false
        console.error('内容提取失败:', error)
        this.$message.error('内容提取失败: ' + error.message)
      }
    },

    /**
     * 复制到剪贴板
     */
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(text)
          .then(() => {
            this.$message.success('内容已复制到剪贴板')
          })
          .catch((err) => {
            console.error('复制失败:', err)
            this.fallbackCopyTextToClipboard(text)
          })
      } else {
        this.fallbackCopyTextToClipboard(text)
      }
    },

    /**
     * 兼容性复制方法
     */
    fallbackCopyTextToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.top = '0'
      textArea.style.left = '0'
      textArea.style.position = 'fixed'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        const successful = document.execCommand('copy')
        if (successful) {
          this.$message.success('内容已复制到剪贴板')
        } else {
          this.$message.error('复制失败')
        }
      } catch (err) {
        console.error('复制失败:', err)
        this.$message.error('复制失败')
      }

      document.body.removeChild(textArea)
    }
  }
}
</script>

<style lang="scss">
// 当前页面URL显示样式
.current-url-info {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 3px;
  border-left: 3px solid #409eff;

  i {
    margin-right: 4px;
    color: #409eff;
  }

  .url-text {
    word-break: break-all;
    font-family: 'Courier New', monospace;
  }
}

// 内容显示样式
.content-display {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
  background-color: #fafafa;
  word-wrap: break-word;
  word-break: break-all;
}

.text-content {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
  background-color: #f9f9f9;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.html-content {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
  background-color: #f5f5f5;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
