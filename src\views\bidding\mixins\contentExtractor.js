/**
 * 内容提取混入
 * 专门处理网页内容提取、登录状态检查等业务功能
 */
import TurndownService from 'turndown'
import { callBailianAPI } from '@/utils/aiRequest'
import websiteUtils from '../websiteUtils.js'

export default {
  data() {
    return {
      // 内容提取相关
      webContent: null,
      activeTab: 'basic',
      extracting: false,

      // 登录状态
      loginStatus: {
        isLoggedIn: false,
        hasLoginCookie: false,
        hasLoginElement: false,
        needLogin: false
      }
    }
  },

  methods: {
    /**
     * 检查登录状态
     */
    async checkLoginStatus() {
      if (!this.$refs.webview || !this.webviewLoaded) {
        this.$message.warning('请先加载网页')
        return
      }

      try {
        const result = await this.$refs.webview.executeJavaScript(`
          (function() {
            const currentCookies = document.cookie;
            const currentUrl = window.location.href;
            console.log('当前URL:', currentUrl);
            console.log('当前Cookies:', currentCookies);

            // 判断是否为千里马网站
            const isQianlimaWebsite = currentUrl.includes('qianlima.com') || currentUrl.includes('qlm.com');

            // 检查是否有有效的登录Cookie
            const cookiePairs = currentCookies.split(';').map(c => c.trim());
            let hasValidLoginCookie = false;
            let loginMethod = '';
            let userInfo = {};

            for (const pair of cookiePairs) {
              const [key, value] = pair.split('=');
              if (key && value) {
                if (isQianlimaWebsite) {
                  // 千里马网站特殊检测逻辑
                  if (key === 'qlm_username' && value && value !== '' && value !== 'null' && value !== 'undefined') {
                    hasValidLoginCookie = true;
                    loginMethod = '千里马用户名Cookie';
                    userInfo.username = decodeURIComponent(value);
                  } else if (key === 'xAuthToken' && value && value !== '' && value !== 'null' && value !== 'undefined') {
                    hasValidLoginCookie = true;
                    loginMethod = '千里马认证Token';
                    userInfo.token = value;
                  }
                } else {
                  // 通用网站登录检测
                  if ((key === 'login' && value === 'true') ||
                      (key === 'session' && value && value !== '' && value !== 'null' && value !== 'undefined') ||
                      (key === 'token' && value && value !== '' && value !== 'null' && value !== 'undefined') ||
                      (key === 'auth' && value && value !== '' && value !== 'null' && value !== 'undefined') ||
                      (key.includes('user') && value && value !== '' && value !== 'null' && value !== 'undefined')) {
                    hasValidLoginCookie = true;
                    loginMethod = '通用登录Cookie: ' + key;
                  }
                }
              }
            }

            // 检测登录相关的DOM元素
            const logoutElements = document.querySelectorAll('.logout, [class*="logout"], a[href*="logout"], button[onclick*="logout"]');
            const userElements = document.querySelectorAll('.user-info, .username, .user-name, .user-avatar, [class*="user-info"], [class*="username"]');
            const hasLoginElement = logoutElements.length > 0 || userElements.length > 0;

            // 检测登录表单（表示需要登录）
            const loginForms = document.querySelectorAll('form[action*="login"], .login-form, input[type="password"], [class*="login-form"]');
            const loginButtons = document.querySelectorAll('button[onclick*="login"], a[href*="login"], .login-btn, [class*="login-btn"]');
            const hasLoginForm = loginForms.length > 0 || loginButtons.length > 0;

            // 检测页面文本内容
            const bodyText = document.body.textContent || '';
            const hasLogoutText = bodyText.includes('退出') || bodyText.includes('登出') || bodyText.includes('logout') || bodyText.includes('Logout');
            const hasLoginText = bodyText.includes('登录') || bodyText.includes('login') || bodyText.includes('Login');

            // 检查URL是否包含登录页面标识
            const isLoginPage = currentUrl.includes('login') || currentUrl.includes('signin') || currentUrl.includes('auth');

            // 综合判断登录状态
            let isLoggedIn = false;
            let needLogin = false;

            if (isQianlimaWebsite) {
              // 千里马网站：主要依赖Cookie检测
              isLoggedIn = hasValidLoginCookie;
              needLogin = !isLoggedIn && (hasLoginForm || isLoginPage);
            } else {
              // 其他网站：综合判断，大多数网站无需登录
              isLoggedIn = hasValidLoginCookie || (hasLoginElement && !isLoginPage) || (hasLogoutText && !isLoginPage);
              needLogin = (hasLoginForm || hasLoginText || isLoginPage) && !isLoggedIn;
            }

            return {
              isQianlimaWebsite: isQianlimaWebsite,
              hasLoginCookie: hasValidLoginCookie,
              loginMethod: loginMethod,
              userInfo: userInfo,
              hasLoginElement: hasLoginElement,
              hasLoginForm: hasLoginForm,
              hasLogoutText: hasLogoutText,
              hasLoginText: hasLoginText,
              isLoginPage: isLoginPage,
              isLoggedIn: isLoggedIn,
              needLogin: needLogin,
              currentUrl: currentUrl,
              cookies: currentCookies,
              title: document.title,
              timestamp: new Date().getTime()
            };
          })()
        `)

        // 更新登录状态
        this.loginStatus = result
        console.log('登录状态检查结果:', result)

        // 显示检测结果
        if (result.isLoggedIn) {
          let message = '✅ 检测到已登录状态'
          if (result.isQianlimaWebsite) {
            message += ` (千里马网站)`
            if (result.userInfo.username) {
              message += ` - 用户: ${result.userInfo.username}`
            }
          }
          this.$message.success(message)
        } else if (result.needLogin) {
          let message = '⚠️ 检测到需要登录'
          if (result.isQianlimaWebsite) {
            message += ' (千里马网站需要登录才能获取完整内容)'
          }
          message += '，请在预览区域中完成登录'
          this.$message.warning(message)
        } else {
          let message = 'ℹ️ '
          if (result.isQianlimaWebsite) {
            message += '千里马网站未检测到登录状态'
          } else {
            message += '该网站无需登录或登录状态未知'
          }
          this.$message.info(message)
        }
      } catch (error) {
        console.error('检查登录状态失败:', error)
        this.$message.error('检查登录状态失败: ' + error.message)
      }
    },

    /**
     * 从webview提取内容
     */
    async extractContentFromWebview() {
      if (!this.$refs.webview) {
        this.$message.warning('请先加载网页')
        return
      }

      this.extracting = true
      const currentUrl = await this.getCurrentPageUrl()
      const rule = websiteUtils.getSiteExtractionRule(currentUrl || this.webviewUrl)

      try {
        const result = await this.$refs.webview.executeJavaScript(`
          (function() {
            const currentUrl = window.location.href;
            const isQianlimaWebsite = ${rule.name === 'qianlima'};
            
            // 检查登录状态（用于千里马网站）
            let isLoggedIn = false;
            if (isQianlimaWebsite) {
              const cookies = document.cookie;
              isLoggedIn = cookies.includes('qlm_username=') &&
                          cookies.includes('xAuthToken=') &&
                          !cookies.includes('qlm_username=;');
            }

            // 根据网站类型选择不同的提取策略
            let selectors = '${rule.selector}'.split(',').map((s) => s.trim())
            let websiteType = '${rule.description}';
            let content = null;
            let matchedSelector = '无匹配';
            
            // 尝试匹配选择器
            for (const selector of selectors) {
              const element = document.querySelector(selector);
              if (element && element.innerHTML.trim()) {
                content = element.innerHTML;
                matchedSelector = selector;
                console.log('成功匹配选择器:', selector);
                break;
              }
            }

            // 如果没有匹配到特定内容，使用body内容
            if (!content) {
              content = document.body.innerHTML;
              matchedSelector = 'body (fallback)';
            }

            return {
              title: document.title,
              url: currentUrl,
              html: content,
              specificContent: content,
              textContent: content.replace(/<[^>]*>/g, '').replace(/\\s+/g, ' ').trim(),
              extractedSelector: matchedSelector,
              websiteType: websiteType,
              isQianlimaWebsite: isQianlimaWebsite,
              isLoggedIn: isLoggedIn,
              extractionTime: new Date().toLocaleString(),
              contentLength: content.length,
              extractionQuality: matchedSelector.includes('fallback') ? '低' : '高'
            };
          })()
        `)

        // 处理提取的内容
        const turndownService = new TurndownService()
        const markdown = turndownService.turndown(result.textContent || result.html || '')

        this.webContent = {
          ...result,
          markDownText: markdown,
          extractionRule: `${result.websiteType}智能提取`,
          fetchTime: new Date().toLocaleString()
        }

        this.activeTab = 'basic'
        this.extracting = false

        // 显示提取结果
        let message = `✅ 内容提取成功！`
        if (result.isQianlimaWebsite) {
          message += ` (${result.websiteType})`
          if (result.isLoggedIn) {
            message += ` - 已登录状态，获取完整内容`
          } else {
            message += ` - 未登录状态，内容可能不完整`
          }
        } else {
          message += ` (${result.websiteType})`
        }

        this.$message.success(message)

        console.log('内容提取成功:', {
          网站类型: result.websiteType,
          是否千里马: result.isQianlimaWebsite,
          登录状态: result.isLoggedIn,
          提取选择器: result.extractedSelector,
          内容长度: result.contentLength,
          提取质量: result.extractionQuality
        })
      } catch (error) {
        this.extracting = false
        console.error('内容提取失败:', error)
        this.$message.error('内容提取失败: ' + error.message)
      }
    },

    /**
     * AI分析内容
     */
    async aiAnalysis() {
      if (!this.webContent || !this.webContent.markDownText) {
        this.$message.warning('请先提取网页内容')
        return
      }

      const systemPrompt = `你是一个专注于招投标信息提取的助手。请从所给网页文本中识别并提取与采购意向、招标公告或中标结果相关的信息...`

      try {
        const data = await callBailianAPI(systemPrompt, this.webContent.markDownText)
        console.log('AI分析结果:', data)
        // 这里可以添加AI分析结果的处理逻辑
      } catch (error) {
        console.error('AI分析失败:', error)
        this.$message.error('AI分析失败')
      }
    }
  }
}
