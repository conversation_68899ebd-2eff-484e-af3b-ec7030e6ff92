<template>
  <div v-loading.fullscreen="webviewLoading" element-loading-text="拼命加载中" class="webview-box">
    <webview ref="webview" :src="webviewUrl" style="width: 100%; height: 100%" partition="persist:main"></webview>
  </div>
</template>
<script>
import websiteUtils from '../websiteUtils.js'
import TurndownService from 'turndown'

export default {
  name: '',
  props: {
    url: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      webviewUrl: '',
      webviewLoading: false, // 是否正在加载
      webviewLoaded: false, // 是否已加载完成
      webviewEventHandlers: null, // webview事件处理器
      extracting: false, // 是否正在提取内容
      webContent: null // 提取内容
    }
  },
  watch: {
    webviewLoaded: {
      immediate: true,
      handler(newVal) {
        this.$emit('webview-loaded', newVal)
      }
    }
  },
  created() {},

  methods: {
    // 绑定webview事件
    bindWebviewEvents() {
      const webview = this.$refs.webview
      if (!webview) {
        console.log('webview引用不存在')
        return
      }
      console.log(this.webviewUrl)

      console.log('绑定webview事件')
      this.webviewLoading = true
      this.webviewLoaded = false

      // 清除之前的事件监听器（如果存在）
      if (this.webviewEventHandlers) {
        this.removeWebviewEvents()
      }

      // 创建事件处理器
      this.webviewEventHandlers = {
        domReady: async () => {
          console.log('webview DOM ready')
          this.webviewLoading = false
          this.webviewLoaded = true
          // this.$message.success('网页加载完成！')
          if (websiteUtils.getSiteExtractionRule(this.url).name === 'qianlima') {
            // 延迟检查登录状态
            this.injectLinkInterceptor()
            const currentUrl = await this.getCurrentPageUrl()
            this.$emit('update:url', currentUrl)
            // this.webviewUrl = currentUrl
            // setTimeout(() => {
            //   this.checkLoginStatus()
            //   // 注入链接拦截脚本
            //   this.injectLinkInterceptor()
            // }, 1500)
          }
          // webview.openDevTools()
        },

        startLoading: () => {
          console.log('webview start loading')
          this.webviewLoading = true
        },

        stopLoading: () => {
          console.log('webview stop loading')
          this.webviewLoading = false
        },

        failLoad: (event) => {
          console.error('webview load failed:', event)
          this.webviewLoading = false
          // 不设置webviewLoaded为false，因为可能只是某些资源加载失败
          // 不显示错误提示，避免干扰正常浏览体验
          // 只有在主页面加载失败时才需要用户知道
          // if (event.errorCode === -3) {
          //   // ERR_ABORTED，通常是用户取消或重定向
          //   console.log('页面加载被中止，这通常是正常的')
          // } else if (event.errorCode < -100) {
          //   // 网络错误
          //   console.error('网络错误，错误代码:', event.errorCode)
          //   // 只在严重错误时才提示用户
          //   if (event.errorCode === -106) {
          //     // ERR_INTERNET_DISCONNECTED
          //     this.$message.error('网络连接已断开')
          //   }
          // }
        }

        // titleUpdated: (event) => {
        //   console.log('页面标题:', event.title)
        // },
      }

      // 绑定事件
      webview.addEventListener('dom-ready', this.webviewEventHandlers.domReady)
      webview.addEventListener('did-start-loading', this.webviewEventHandlers.startLoading)
      webview.addEventListener('did-stop-loading', this.webviewEventHandlers.stopLoading)
      webview.addEventListener('did-fail-load', this.webviewEventHandlers.failLoad)
      // webview.addEventListener('page-title-updated', this.webviewEventHandlers.titleUpdated)
    },

    // 移除webview事件监听器
    removeWebviewEvents() {
      const webview = this.$refs.webview
      if (!webview || !this.webviewEventHandlers) return

      console.log('移除webview事件监听器')

      webview.removeEventListener('dom-ready', this.webviewEventHandlers.domReady)
      webview.removeEventListener('did-start-loading', this.webviewEventHandlers.startLoading)
      webview.removeEventListener('did-stop-loading', this.webviewEventHandlers.stopLoading)
      webview.removeEventListener('did-fail-load', this.webviewEventHandlers.failLoad)
      // webview.removeEventListener('page-title-updated', this.webviewEventHandlers.titleUpdated)
      this.webviewEventHandlers = null
    },
    // 注入链接拦截脚本
    async injectLinkInterceptor() {
      if (!this.$refs.webview) return

      try {
        await this.$refs.webview.executeJavaScript(`
          (function() {
            // 如果已经注入过，则不重复注入
            if (window.linkInterceptorInjected) return;
            window.linkInterceptorInjected = true;

            console.log('注入简化版链接拦截脚本');

            // 拦截所有链接点击事件
            document.addEventListener('click', function(event) {
              const target = event.target.closest('a');
              if (!target) return;

              const href = target.href;
              if (!href) return;

              // 检查是否是会打开新窗口的链接
              const shouldIntercept = target.target === '_blank' ||
                                    target.target === '_new' ||
                                    target.rel === 'noopener' ||
                                    target.rel === 'noreferrer';

              // 如果是新窗口链接，改为当前窗口打开
              if (shouldIntercept) {
                console.log('拦截新窗口链接，改为当前窗口打开:', href);
                target.target = '_self'; // 改为当前窗口打开
                // 不阻止默认行为，让链接正常跳转
              }
            }, true);

            // 拦截window.open调用
            const originalOpen = window.open;
            window.open = function(url, name, features) {
              console.log('拦截window.open调用，改为当前窗口跳转:', url);
              // 直接在当前窗口跳转
              if (url) {
                window.location.href = url;
              }
              return null; // 返回null表示没有打开新窗口
            };

            // 拦截表单的target="_blank"提交
            document.addEventListener('submit', function(event) {
              const form = event.target;
              if (form.target === '_blank') {
                console.log('拦截表单新窗口提交，改为当前窗口:', form.action);
                form.target = '_self'; // 改为当前窗口提交
              }
            }, true);
            console.log('简化版链接拦截脚本注入完成');
          })();
        `)
        console.log('链接拦截脚本注入成功')
      } catch (error) {
        console.error('注入链接拦截脚本失败:', error)
      }
    },
    /**
     * 获取当前页面URL（纯粹获取，不做任何处理）
     */
    async getCurrentPageUrl() {
      if (!this.$refs.webview) return null

      try {
        const currentUrl = await this.$refs.webview.executeJavaScript('window.location.href')
        return currentUrl
      } catch (error) {
        console.log('获取当前URL失败:', error)
        return null
      }
    },
    // 从webview提取内容
    async extractContentFromWebview() {
      if (!this.$refs.webview) {
        this.$message.warning('请先加载网页')
        return
      }

      this.extracting = true
      const rule = websiteUtils.getSiteExtractionRule(this.url)
      try {
        const result = await this.$refs.webview.executeJavaScript(`
          (function() {
            const currentUrl = window.location.href;
            const isQianlimaWebsite = ${rule.name === 'qianlima'};
            // 检查登录状态（用于千里马网站）
            let isLoggedIn = false;
            if (isQianlimaWebsite) {
              const cookies = document.cookie;
              isLoggedIn = cookies.includes('qlm_username=') &&
                          cookies.includes('xAuthToken=') &&
                          !cookies.includes('qlm_username=;');
            }

            // 根据网站类型选择不同的提取策略
            let selectors = '${rule.selector}'.split(',').map((s) => s.trim())
            let websiteType = '${rule.description}';
            let content = null;
            let matchedSelector = '无匹配';
            // 尝试匹配选择器
            for (const selector of selectors) {
              const element = document.querySelector(selector);
              if (element && element.innerHTML.trim()) {
                content = element.innerHTML;
                matchedSelector = selector;
                console.log('成功匹配选择器:', selector);
                break;
              }
            }

            // 如果没有匹配到特定内容，使用body内容
            if (!content) {
              content = document.body.innerHTML;
              matchedSelector = 'body (fallback)';
            }

            return {
              title: document.title,
              url: currentUrl,
              html: content,
              specificContent: content,
              textContent: content.replace(/<[^>]*>/g, '').replace(/\\s+/g, ' ').trim(),
              extractedSelector: matchedSelector,
              websiteType: websiteType,
              isQianlimaWebsite: isQianlimaWebsite,
              isLoggedIn: isLoggedIn,
              extractionTime: new Date().toLocaleString(),
              contentLength: content.length,
              // 提取质量评估
              extractionQuality: matchedSelector.includes('fallback') ? '低' : '高'
            };
          })()
        `)

        // 处理提取的内容
        const turndownService = new TurndownService()
        const markdown = turndownService.turndown(result.textContent || result.html || '')

        this.webContent = {
          ...result,
          markDownText: markdown,
          extractionRule: `${result.websiteType}智能提取`,
          fetchTime: new Date().toLocaleString()
        }
        console.log(this.webContent)

        // this.activeTab = 'basic'
        this.extracting = false

        // 显示提取结果
        let message = `✅ 内容提取成功！`
        if (result.isQianlimaWebsite) {
          message += ` (${result.websiteType})`
          if (result.isLoggedIn) {
            message += ` - 已登录状态，获取完整内容`
          } else {
            message += ` - 未登录状态，内容可能不完整`
          }
        } else {
          message += ` (${result.websiteType})`
        }

        this.$message.success(message)

        // 显示详细通知
        if (result.isQianlimaWebsite) {
          if (result.isLoggedIn) {
            this.$notify({
              title: '千里马网站 - 登录状态',
              message: '检测到您已登录，提取的内容包含登录后的完整信息',
              type: 'success',
              duration: 5000
            })
          } else {
            this.$notify({
              title: '千里马网站 - 未登录',
              message: '未检测到登录状态，建议先登录以获取完整内容',
              type: 'warning',
              duration: 5000
            })
          }
        }

        console.log('内容提取成功:', {
          网站类型: result.websiteType,
          是否千里马: result.isQianlimaWebsite,
          登录状态: result.isLoggedIn,
          提取选择器: result.extractedSelector,
          内容长度: result.contentLength,
          提取质量: result.extractionQuality
        })
      } catch (error) {
        this.extracting = false
        console.error('内容提取失败:', error)
        this.$message.error('内容提取失败: ' + error.message)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.webview-box {
  width: 100%;
  height: 800px;
}
</style>
