/**
 * 链接拦截混入 - 纯粹的链接拦截，不做任何业务处理
 * 只负责：1. 拦截新窗口链接 2. 提取链接URL 3. 让链接在当前窗口跳转
 */
export default {
  data() {
    return {
      // 链接拦截相关
      interceptedUrl: '' // 最后一次拦截到的链接URL
    }
  },

  methods: {
    /**
     * 注入纯粹的链接拦截脚本
     * 只修改target属性，不做任何其他处理
     */
    async injectPureLinkInterceptor() {
      if (!this.$refs.webview) return

      try {
        await this.$refs.webview.executeJavaScript(`
          (function() {
            // 防止重复注入
            if (window.pureLinkInterceptorInjected) return;
            window.pureLinkInterceptorInjected = true;

            console.log('注入纯粹链接拦截脚本');

            // 拦截链接点击，只修改target，不阻止跳转
            document.addEventListener('click', function(event) {
              const target = event.target.closest('a');
              if (!target || !target.href) return;

              // 检查是否是新窗口链接
              const isNewWindow = target.target === '_blank' || 
                                target.target === '_new' ||
                                target.rel === 'noopener' ||
                                target.rel === 'noreferrer';

              if (isNewWindow) {
                console.log('拦截新窗口链接:', target.href);
                // 只修改target，让链接在当前窗口打开
                target.target = '_self';
                // 不阻止默认行为，让浏览器自然跳转
              }
            }, true);

            // 拦截window.open，改为当前窗口跳转
            const originalOpen = window.open;
            window.open = function(url, name, features) {
              console.log('拦截window.open:', url);
              if (url) {
                window.location.href = url;
              }
              return null;
            };

            // 拦截表单新窗口提交
            document.addEventListener('submit', function(event) {
              const form = event.target;
              if (form.target === '_blank') {
                console.log('拦截表单新窗口提交:', form.action);
                form.target = '_self';
              }
            }, true);

            console.log('纯粹链接拦截脚本注入完成');
          })();
        `)
        console.log('链接拦截脚本注入成功')
      } catch (error) {
        console.error('注入链接拦截脚本失败:', error)
      }
    },

    /**
     * 处理新窗口请求事件
     * 只记录URL，不做任何跳转处理
     */
    handleNewWindow(event) {
      debugger
      console.log('捕获到新窗口请求:', event.url)
      // 阻止新窗口打开
      event.preventDefault()
      // 只记录URL，不做任何处理
      this.interceptedUrl = event.url
      // 触发自定义事件，让父组件知道有链接被拦截
      this.$emit('link-intercepted', event.url)
    },

    /**
     * 获取当前页面URL（纯粹获取，不做任何处理）
     */
    async getCurrentPageUrl() {
      if (!this.$refs.webview) return null

      try {
        const currentUrl = await this.$refs.webview.executeJavaScript('window.location.href')
        return currentUrl
      } catch (error) {
        console.log('获取当前URL失败:', error)
        return null
      }
    }
  }
}
