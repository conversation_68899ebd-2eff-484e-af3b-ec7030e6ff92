<template>
  <div class="app-container">
    <!-- 输入和控制区域 -->
    <el-row :gutter="10" style="margin-bottom: 20px">
      <el-col :span="16">
        <el-input v-model="url" placeholder="请输入网页地址" clearable>
          <template slot="prepend">🌐</template>
        </el-input>
      </el-col>
      <el-col :span="8">
        <el-button type="primary" :disabled="!url" @click="loadInWebview"> 🌐 加载预览 </el-button>
        <el-button type="success" :disabled="!webview_loaded" @click="extractContentFromWebview"> 📄 提取内容 </el-button>
        <!-- <el-button type="info" :disabled="!webviewLoaded" @click="checkLoginStatus"> 🔍 检查登录状态 </el-button>
        <el-button type="warning" :disabled="!webviewLoaded" @click="refreshWebview"> 🔄 刷新 </el-button> -->
      </el-col>
    </el-row>
    <!-- 预览区域 -->
    <WebviewComponent ref="webviewRef" :url.sync="url" @webview-loaded="webview_loaded = $event" />
  </div>
</template>
<script>
import WebviewComponent from './components/Webview.vue'
export default {
  name: '',
  components: { WebviewComponent }, // 注册Webview组件
  data() {
    return {
      url: '',
      webview_loaded: false
    }
  },

  created() {},
  methods: {
    loadInWebview() {
      if (!this.url) {
        this.$message.warning('请输入网页地址')
        return
      }
      const webviewRef = this.$refs['webviewRef']

      webviewRef.webviewUrl = this.url
      webviewRef.bindWebviewEvents()
    },
    extractContentFromWebview() {
      this.$refs['webviewRef'].extractContentFromWebview()
    }
  }
}
</script>
<style scoped lang="scss"></style>
